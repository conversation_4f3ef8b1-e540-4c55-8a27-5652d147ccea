/** @type {import('tailwindcss').Config} */
module.exports = {
    darkMode: "media",
    content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
    theme: {
        extend: {
            colors: {
                earthy: {
                    DEFAULT: "#a68a64",
                    light: "#f4efe9",
                    dark: "#5c4b3b",
                },
            },
            borderWidth: {
                5: "5px",
            },
            animation: {
                "slide-down": "slideDown 0.3s ease-out",
            },
            keyframes: {
                slideDown: {
                    "0%": { opacity: "0", transform: "translateY(-10px)" },
                    "100%": { opacity: "1", transform: "translateY(0)" },
                },
            },
        },
    },
    plugins: [],
};
