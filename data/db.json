{"projects": [{"id": 1, "title": "<PERSON><PERSON><PERSON>", "description": "A productivity dashboard for developers to track tasks, goals, and inspiration.", "image": "/images/project-1.png", "url": "https://example.com", "date": "2025-02-01", "category": "Tools", "featured": false}, {"id": 2, "title": "SnapFeed", "description": "A photo-sharing app with uploads, a feed, and social features.", "image": "/images/project-2.png", "url": "https://example.com", "date": "2025-01-20", "category": "Mobile", "featured": true}, {"id": 3, "title": "NoteNest", "description": "A note-taking app with categories and local storage support.", "image": "/images/project-3.png", "url": "https://example.com", "date": "2024-12-15", "category": "Frontend", "featured": false}, {"id": 4, "title": "FitTrack", "description": "A fitness tracker that logs workouts and progress visually.", "image": "/images/project-4.png", "url": "https://example.com", "date": "2024-11-05", "category": "Mobile", "featured": false}, {"id": 5, "title": "CodeCritic", "description": "A code snippet review tool with comments and upvotes.", "image": "/images/project-5.png", "url": "https://example.com", "date": "2024-10-12", "category": "Tools", "featured": false}, {"id": 6, "title": "InspoQuote", "description": "A daily inspiration quote generator with save/share options.", "image": "/images/project-6.png", "url": "https://example.com", "date": "2024-09-28", "category": "Frontend", "featured": false}, {"id": 7, "title": "Blogify", "description": "A minimalist blogging platform with Markdown and comments.", "image": "/images/project-7.png", "url": "https://example.com", "date": "2024-09-01", "category": "Fullstack", "featured": false}, {"id": 8, "title": "StudySync", "description": "A study planner with spaced repetition and progress tracking.", "image": "/images/project-1.png", "url": "https://example.com", "date": "2024-08-20", "category": "Frontend", "featured": false}, {"id": 9, "title": "TaskTide", "description": "An intuitive kanban board to manage tasks across projects.", "image": "/images/project-2.png", "url": "https://example.com", "date": "2024-08-01", "category": "Tools", "featured": false}, {"id": 10, "title": "CryptoView", "description": "A real-time dashboard to track crypto market prices and trends.", "image": "/images/project-3.png", "url": "https://example.com", "date": "2024-07-15", "category": "Backend", "featured": true}, {"id": 11, "title": "MindBoard", "description": "A collaborative mind-mapping and brainstorming tool.", "image": "/images/project-4.png", "url": "https://example.com", "date": "2024-06-30", "category": "Frontend", "featured": false}, {"id": 12, "title": "BookBase", "description": "A personal library app to manage your books and reading goals.", "image": "/images/project-5.png", "url": "https://example.com", "date": "2024-06-01", "category": "Fullstack", "featured": false}, {"id": 13, "title": "StreamScout", "description": "Find and track your favorite shows across streaming platforms.", "image": "/images/project-6.png", "url": "https://example.com", "date": "2024-05-15", "category": "Backend", "featured": true}, {"id": 14, "title": "EventEdge", "description": "Create and manage events with RSVPs and calendar sync.", "image": "/images/project-7.png", "url": "https://example.com", "date": "2024-05-01", "category": "Fullstack", "featured": false}, {"id": 15, "title": "LangLink", "description": "A language exchange platform to connect global learners.", "image": "/images/project-1.png", "url": "https://example.com", "date": "2024-04-18", "category": "Mobile", "featured": false}, {"id": 16, "title": "EcoSaver", "description": "A green habits tracker to build eco-friendly routines.", "image": "/images/project-2.png", "url": "https://example.com", "date": "2024-04-01", "category": "Tools", "featured": true}, {"id": 17, "title": "QuizQuest", "description": "A quiz app with leaderboards and question packs by topic.", "image": "/images/project-3.png", "url": "https://example.com", "date": "2024-03-10", "category": "Frontend", "featured": false}]}