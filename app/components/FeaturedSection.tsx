import type { Project } from "~/types/types";
import ProjectList from "./ProjectList";

const FeaturedSection = ({
    projects,
    count,
}: {
    projects: Project[];
    count: number;
}) => {
    const featuredProjects = projects
        .filter((project) => project.featured)
        .slice(0, count);

    return (
        <div>
            <div className="mt-10 pt-10">
                <h2 className="text-2xl font-bold text-black dark:text-white mb-2">
                    Projects That Made Me Level Up! 🎉
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-8">
                    Here are some of my wildest builds each one pushed my brain
                    to the edge (and yes, there were snacks and maybe a few
                    happy tears). Dive in and enjoy!
                </p>
            </div>
            <ProjectList projects={featuredProjects} />
        </div>
    );
};

export default FeaturedSection;
