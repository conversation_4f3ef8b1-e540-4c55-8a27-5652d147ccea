import Badge from "../ui/badge";
import { Code, Server, Wrench } from "lucide-react";

const categories = [
    {
        name: "Frontend",
        icon: <Code className="w-4 h-4 mr-1 inline" />,
        tech: ["React", "Next.js", "TypeScript", "HTML", "CSS", "TailwindCSS"],
    },
    {
        name: "Backend",
        icon: <Server className="w-4 h-4 mr-1 inline" />,
        tech: ["Node.js", "Express", "REST API", "MongoDB", "PostgreSQL"],
    },
    {
        name: "Tools & Others",
        icon: <Wrench className="w-4 h-4 mr-1 inline" />,
        tech: ["GitHub", "Docker", "Vite", "Styled Components"],
    },
];

const TechStack = () => {
    return (
        <section className="max-w-5xl mx-auto my-4">
            <p className="mb-6 " data-aos="fade-zoom-in" data-aos-delay="150">
                Tools and technologies I work with regularly to build modern web
                apps.
            </p>

            <div className="grid sm:grid-cols-2 md:grid-cols-3 gap-6">
                {categories.map((cat, i) => (
                    <div
                        key={cat.name}
                        className=""
                        data-aos="zoom-in-up"
                        data-aos-delay={i * 150}
                    >
                        <h4 className="text-xl font-semibold mb-4 text-gray-800">
                            {cat.icon} {cat.name}
                        </h4>
                        <div className="flex flex-wrap justify-left gap-3">
                            {cat.tech.map((tech) => (
                                <Badge key={tech}>{tech}</Badge>
                            ))}
                        </div>
                    </div>
                ))}
            </div>
        </section>
    );
};

export default TechStack;
