import Badge from "~/ui/badge";
import type { Project } from "~/types/types";
import { Link } from "react-router";

type ProjectCard = {
    project: Project;
    delay: number;
    setSelectedCategory: (category: string) => void;
    setCurrentPage: (currentPage: number) => void;
};

const ProjectCard = ({
    project,
    delay = 0,
    setCurrentPage,
    setSelectedCategory,
}: ProjectCard) => {
    if (!project) return null;
    const { id, title, description, image, url, date, category, featured } =
        project;
    return (
        <div
            data-aos="fade-zoom-in"
            data-aos-delay={delay}
            className="group bg-white dark:bg-gray-800 border-2 border-b-4 border-gray-300 dark:border-gray-600 rounded-2xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-200"
        >
            <Link to={`/projects/${id}`}>
                {image && (
                    <img
                        src={image}
                        alt={title}
                        className="w-full h-20 object-cover border-b border-gray-200 dark:border-gray-600"
                    />
                )}
            </Link>
            <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                    <button
                        className="cursor-pointer"
                        onClick={() => {
                            setSelectedCategory(category);
                            setCurrentPage(1);
                        }}
                    >
                        <Badge className="text-xs uppercase tracking-wider">
                            {category}
                        </Badge>
                    </button>
                    <span className="text-xs text-gray-400 dark:text-gray-500">
                        {date}
                    </span>
                </div>
                <Link to={`/projects/${id}`}>
                    <h3 className="text-lg font-semibold text-black dark:text-white mb-1">
                        {title}
                    </h3>

                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        {description}
                    </p>
                </Link>
            </div>

            <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block p-4 text-sm font-medium text-black dark:text-white hover:text-earthy dark:hover:text-earthy-light transition-colors"
            >
                View Project →
            </a>
        </div>
    );
};

export default ProjectCard;
