import Badge from "~/ui/badge";
import type { Project } from "~/types/types";
import { Link } from "react-router";

type ProjectCard = {
    project: Project;
    delay: number;
    setSelectedCategory: (category: string) => void;
    setCurrentPage: (currentPage: number) => void;
};

const ProjectCard = ({
    project,
    delay = 0,
    setCurrentPage,
    setSelectedCategory,
}: ProjectCard) => {
    if (!project) return null;
    const { id, title, description, image, url, date, category, featured } =
        project;
    return (
        <div
            data-aos="fade-zoom-in"
            data-aos-delay={delay}
            className="group bg-white border-2 border-b-4 border-gray-300 rounded-2xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200"
        >
            <Link to={`/projects/${id}`}>
                {image && (
                    <img
                        src={image}
                        alt={title}
                        className="w-full h-20 object-cover border-b border-gray-200"
                    />
                )}
            </Link>
            <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                    <button
                        className="cursor-pointer"
                        onClick={() => {
                            setSelectedCategory(category);
                            setCurrentPage(1);
                        }}
                    >
                        <Badge className="text-xs uppercase tracking-wider text-gray-500">
                            {category}
                        </Badge>
                    </button>
                    <span className="text-xs text-gray-400">{date}</span>
                </div>
                <Link to={`/projects/${id}`}>
                    <h3 className="text-lg font-semibold text-black mb-1">
                        {title}
                    </h3>

                    <p className="text-sm text-gray-600 mb-4">{description}</p>
                </Link>
            </div>

            <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block p-4 text-sm font-medium text-black  hover:text-stone-700 transition-colors"
            >
                View Project →
            </a>
        </div>
    );
};

export default ProjectCard;
