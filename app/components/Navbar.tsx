import { NavLink } from "react-router";
import { useState } from "react";
import { BackpackIcon, MenuIcon, XIcon } from "lucide-react";

const navLinks = [
    { to: "/", label: "Home" },
    { to: "/projects", label: "Projects" },
    { to: "/blog", label: "Blog" },
    { to: "/about", label: "About" },
    { to: "/contact", label: "Contact" },
];

const Navbar = () => {
    const [open, setOpen] = useState(false);

    return (
        <nav className="border-b bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 sticky top-0 z-50">
            <div className="max-w-5xl mx-auto py-3 flex items-center justify-between">
                {/* Logo */}
                <NavLink
                    to="/"
                    className="text-black dark:text-white flex items-center gap-2"
                >
                    <BackpackIcon className="w-5 h-5" />
                    <span className="font-medium text-sm"><PERSON><PERSON><PERSON></span>
                </NavLink>

                {/* Desktop Nav */}
                <div className="hidden md:flex gap-6 text-sm text-gray-700 dark:text-gray-300">
                    {navLinks.map((link) => (
                        <NavLink
                            key={link.to}
                            to={link.to}
                            className={({ isActive }) =>
                                `transition-colors pb-1 ${
                                    isActive
                                        ? "text-black dark:text-white border-b-2 border-black dark:border-white"
                                        : "hover:text-black dark:hover:text-white"
                                }`
                            }
                        >
                            {link.label}
                        </NavLink>
                    ))}
                </div>

                {/* Mobile menu toggle */}
                <div className="md:hidden">
                    <button
                        onClick={() => setOpen(!open)}
                        className="text-gray-700 dark:text-gray-300 p-1"
                    >
                        {open ? (
                            <XIcon className="w-5 h-5" />
                        ) : (
                            <MenuIcon className="w-5 h-5" />
                        )}
                    </button>
                </div>
            </div>

            {/* Mobile Nav */}
            {open && (
                <div className="md:hidden px-4 pb-4">
                    <div className="flex flex-col space-y-2 text-sm text-gray-700 dark:text-gray-300">
                        {navLinks.map((link) => (
                            <NavLink
                                key={link.to}
                                to={link.to}
                                onClick={() => setOpen(false)}
                                className={({ isActive }) =>
                                    `transition-colors py-1 ${
                                        isActive
                                            ? "text-black dark:text-white font-medium"
                                            : "hover:text-black dark:hover:text-white"
                                    }`
                                }
                            >
                                {link.label}
                            </NavLink>
                        ))}
                    </div>
                </div>
            )}
        </nav>
    );
};

export default Navbar;
