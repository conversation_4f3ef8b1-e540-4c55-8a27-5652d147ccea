import ProjectCard from "./ProjectCard";
import type { Project } from "~/types/types";
import { useState } from "react";
import RenderPagination from "~/ui/pagination";
import Badge from "~/ui/badge";
import { AnimatePresence, motion } from "framer-motion";

const ProjectList = ({ projects }: { projects: Project[] }) => {
    const [selectedCategory, setSelectedCategory] = useState("ALL");
    // Get Categories
    const categories = [
        "ALL",
        ...new Set(projects.map((project) => project.category)),
    ];

    // Filter project based on the category
    const filteredProjects =
        selectedCategory === "ALL"
            ? projects
            : projects.filter(
                  (project) => project.category === selectedCategory
              );

    const [currentPage, setCurrentPage] = useState(1);
    const projectsPerPage = 6;
    const totalPages = Math.ceil(filteredProjects.length / projectsPerPage);
    const indexOfLast = currentPage * projectsPerPage;
    const indexOfFirst = indexOfLast - projectsPerPage;
    const currentProjects = filteredProjects.slice(indexOfFirst, indexOfLast);

    return (
        <section className="py-4  max-w-6xl mx-auto my-8">
            <div className="flex gap-2 mb-6">
                {categories.map((category, idx) => (
                    <button
                        key={idx}
                        data-aos="fade-zoom-in"
                        data-aos-delay={`${idx * 200}`}
                        onClick={() => {
                            setSelectedCategory(category);
                            setCurrentPage(1);
                        }}
                        className="cursor-pointer"
                    >
                        <Badge>{category}</Badge>
                    </button>
                ))}
            </div>
            <AnimatePresence mode="wait">
                <motion.div
                    layout
                    className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3"
                >
                    {currentProjects.map((project, index) => (
                        <motion.div layout key={project.id}>
                            <ProjectCard
                                project={project}
                                setSelectedCategory={setSelectedCategory}
                                setCurrentPage={setCurrentPage}
                                delay={index * 200}
                            />
                        </motion.div>
                    ))}
                </motion.div>
            </AnimatePresence>
            {totalPages > 1 && (
                <RenderPagination
                    totalPages={totalPages}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                />
            )}
        </section>
    );
};

export default ProjectList;
