import { Link } from "react-router";
import Button from "~/ui/button";
import TechStack from "./TechStack";
import EducationSection from "./Education";

type HeroProp = {
    name?: string;
    title?: string;
    contact?: string;
    description?: string;
};

const Hero = ({
    name = "<PERSON><PERSON><PERSON>",
    title = "Open to Remote | Part-Time | Full-Time",
    contact = "+251 966 339 226 • <EMAIL>",
    description = "I build friendly web experiences and help others become confident, modern developers.",
}: HeroProp) => {
    return (
        <>
            <section className="py-20 text-black dark:text-white max-w-5xl mx-auto my-8 ">
                <div className="flex justify-between ">
                    <div data-aos="fade-up" data-aos-delay="150">
                        <h1 className="text-3xl sm:text-4xl font-bold mb-3">
                            Hey, I’m {name} 👋
                        </h1>

                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                            {title}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-500 mb-6">
                            {contact}
                        </p>

                        <p className="max-w-xl text-base text-gray-700 dark:text-gray-300 mb-8">
                            {description}{" "}
                            <span>
                                <a
                                    href="https://drive.google.com/file/d/1XikLskYXweus7u48pK1IhFYRJ7vpTV4R/view?usp=sharing"
                                    download
                                    className="underline text-sm text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white "
                                >
                                    Download Resume
                                </a>
                            </span>
                        </p>
                    </div>
                    <section>
                        <EducationSection />
                    </section>
                </div>

                <div
                    data-aos="fade-up"
                    data-aos-delay="150"
                    className="flex flex-wrap gap-4 mt-6"
                >
                    <Link to="/projects">
                        <Button className="bg-gradient-to-r from-earthy-light via-earthy to-earthy-dark dark:from-earthy-dark dark:via-earthy dark:to-earthy-light">
                            View Projects
                        </Button>
                    </Link>
                    <Link to="/contact">
                        <Button>Contact Me</Button>
                    </Link>
                </div>
            </section>
            <section>
                <TechStack />
            </section>
        </>
    );
};

export default Hero;
