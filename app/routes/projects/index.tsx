import ProjectList from "~/components/ProjectList";
import type { Route } from "./+types";
import type { Project } from "~/types/types";

export function meta({}: Route.MetaArgs) {
    return [{ title: "Show Me | My Projects" }];
}

export async function loader({
    request,
}: Route.LoaderArgs): Promise<{ projects: Project[] }> {
    const res = await fetch(`http://localhost:8000/projects`);
    const data = await res.json();

    return { projects: data };
}

const ProjectPage = ({ loaderData }: Route.ComponentProps) => {
    const { projects } = loaderData as { projects: Project[] };

    return (
        <>
            <div className="mt-4">
                <h2 className="text-3xl font-bold text-black mb-2">Projects</h2>
                <p className="text-gray-600 mb-8">
                    These are some of the projects I have worked on. Each
                    project showcases different skills and technologies I've
                    learned and applied. Explore the list below to see more
                    details about each project.
                </p>
            </div>
            <ProjectList projects={projects} />
        </>
    );
};

export default ProjectPage;
