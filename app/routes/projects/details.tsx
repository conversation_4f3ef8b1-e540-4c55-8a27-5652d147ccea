import { ArrowLeft } from "lucide-react";
import type { Route } from "./+types";
import type { Project } from "~/types/types";
import Badge from "~/ui/badge";
import { Link } from "react-router";
export interface ComponentProps {
    loaderData: Project;
}
export async function clientLoader({
    request,
    params,
}: Route.ClientLoaderArgs & { params: { id: string } }): Promise<Project> {
    const res = await fetch(
        `${import.meta.env.VITE_API_URL}/projects/${params.id}`
    );

    if (!res.ok) throw new Response("Project not found", { status: 404 });
    const project: Project = await res.json();
    return project;
}

export function HydrateFallback() {
    return <>Loading ....</>;
}

const ProjectDetails = ({ loaderData }: ComponentProps) => {
    const project = loaderData as Project;

    if (!project) {
        return (
            <div className="py-20 text-center text-gray-600">
                Project not found 😢
            </div>
        );
    }

    return (
        <div>
            {project.image && (
                <div className="w-full h-[25vh] relative overflow-hidden">
                    <img
                        src={project.image}
                        alt={project.title}
                        className="object-cover w-full h-full"
                    />
                    <div className="absolute inset-0 bg-black/20" />
                </div>
            )}

            <section className="max-w-5xl mx-auto px-4 py-16">
                <Link to="/projects" className=" flex items-center gap-2">
                    <ArrowLeft />
                </Link>
                <div data-aos="fade-up" className="p-6">
                    <div className="mb-6">
                        <h1 className="text-3xl font-bold text-black mb-2">
                            {project.title}
                        </h1>
                        <div className="text-sm text-gray-500 flex gap-4">
                            <span>{project.category}</span>
                            <span>{project.date}</span>
                        </div>
                    </div>

                    <p className="text-gray-700 text-base leading-relaxed mb-6">
                        {project.description}
                    </p>

                    <div className="text-sm text-gray-600 space-y-4">
                        <p>
                            🚀 This project demonstrates clean frontend
                            architecture and strong attention to UI/UX detail.
                        </p>
                        <p>
                            📦 It features component-based design, responsive
                            layouts, and clean integration with REST APIs.
                        </p>
                        <p>🔍 More exciting projects await on the </p>
                    </div>

                    {/* Visit Project Button */}
                    <div className="mt-8">
                        <Badge className="py-2 rounded-xl  text-black font-semibold hover:scale-[0.98] transition-all">
                            <a
                                href="example.com"
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                🔗 Visit Project
                            </a>
                        </Badge>
                    </div>
                </div>
            </section>
        </div>
    );
};

export default ProjectDetails;
