import type { Route } from "./+types/index";
import FeaturedSection from "~/components/FeaturedSection";
import type { Project } from "~/types/types";

export function meta({}: Route.MetaArgs) {
    return [
        { title: "Show Me | Home" },
        { name: "description", content: "Welcome to where I show me" },
    ];
}

export async function loader({
    request,
}: Route.LoaderArgs): Promise<{ projects: Project[] }> {
    const res = await fetch(`http://localhost:8000/projects`);
    const data = await res.json();
    return { projects: data };
}

export default function Home({ loaderData }: Route.ComponentProps) {
    const { projects } = loaderData as { projects: Project[] };
    const countOfFeatures = 3;
    return (
        <>
            <FeaturedSection projects={projects} count={countOfFeatures} />
        </>
    );
}
