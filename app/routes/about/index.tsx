import type { Route } from "./+types/index";

export function meta({}: Route.MetaArgs) {
    return [
        { title: "Show Me | About me" },
        { name: "description", content: "Welcome to where I show me" },
    ];
}

const AboutPage = () => {
    return (
        <div className="p-4">
            <h2 className="text-2xl font-bold text-black dark:text-white mb-4">
                Hey, I'm me!
            </h2>
            <p className="text-gray-700 dark:text-gray-300">
                Welcome to my about page. More content coming soon!
            </p>
        </div>
    );
};

export default AboutPage;
