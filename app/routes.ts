import {
    type RouteConfig,
    index,
    route,
    layout,
} from "@react-router/dev/routes";

export default [
    layout("./routes/layouts/home.tsx", [index("routes/home/<USER>")]),
    layout("./routes/layouts/main.tsx", [
        route("about", "./routes/about/index.tsx"),
        route("contact", "./routes/contact/index.tsx"),
        route("projects", "./routes/projects/index.tsx"),
        route("projects/:id", "./routes/projects/details.tsx"),
        route("blog", "./routes/blog/index.tsx"),
    ]),

    route(
        "/.well-known/appspecific/com.chrome.devtools.json",
        "routes/debug-null.tsx"
    ),
] satisfies RouteConfig;
5;
