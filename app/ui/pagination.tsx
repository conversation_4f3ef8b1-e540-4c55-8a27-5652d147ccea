type PaginationProp = {
    totalPages: number;
    currentPage: number;
    setCurrentPage: (page: number) => void;
};

const RenderPagination = ({
    totalPages,
    currentPage,
    setCurrentPage,
}: PaginationProp) => (
    <div className="flex justify-center items-center gap-2 mt-4 flex-wrap">
        {Array.from({ length: totalPages }, (_, idx) => {
            const page = idx + 1;
            const isActive = currentPage === page;

            return (
                <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`px-3 text-sm py-1.5 rounded-lg cursor-pointer border-2 transition-all duration-200
                    ${
                        isActive
                            ? "bg-earthy-light dark:bg-earthy-dark border-earthy dark:border-earthy-light text-earthy-dark dark:text-earthy-light font-extrabold shadow-md"
                            : "bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300 hover:border-earthy dark:hover:border-earthy-light hover:text-earthy-dark dark:hover:text-earthy-light"
                    }
                    hover:scale-[0.98] hover:shadow-md`}
                >
                    {page}
                </button>
            );
        })}
    </div>
);

export default RenderPagination;
