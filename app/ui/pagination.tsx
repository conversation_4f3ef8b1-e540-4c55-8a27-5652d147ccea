type PaginationProp = {
    totalPages: number;
    currentPage: number;
    setCurrentPage: (page: number) => void;
};

const RenderPagination = ({
    totalPages,
    currentPage,
    setCurrentPage,
}: PaginationProp) => (
    <div className="flex justify-center items-center gap-2 mt-4 flex-wrap">
        {Array.from({ length: totalPages }, (_, idx) => {
            const page = idx + 1;
            const isActive = currentPage === page;

            return (
                <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`px-3 text-sm py-1 rounded-full cursor-pointer  transition-all duration-200
                    ${
                        isActive
                            ? "bg-white border-black text-black font-extrabold"
                            : "bg-gray-100 border-gray-400 text-gray-600 hover:border-black hover:text-black"
                    }
                    hover:scale-[0.98]`}
                >
                    {page}
                </button>
            );
        })}
    </div>
);

export default RenderPagination;
