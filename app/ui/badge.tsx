interface BadgeProps {
    children: React.ReactNode;
    className?: string;
}

const Badge = ({ children, className = "" }: BadgeProps) => {
    return (
        <span
            className={`inline-block text-sm px-3 py-1 rounded-md border-2 border-b-5 border-earthy dark:border-earthy-light bg-earthy-light dark:bg-earthy-dark text-earthy-dark dark:text-earthy-light shadow-md hover:translate-y-[1px] active:translate-y-[2px] hover:border-b-3 transition-all duration-150 hover:shadow-lg ${className}`}
        >
            {children}
        </span>
    );
};

export default Badge;
