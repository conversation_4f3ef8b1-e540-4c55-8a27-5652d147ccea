interface BadgeProps {
    children: React.ReactNode;
    className?: string;
}

const Badge = ({ children, className = "" }: BadgeProps) => {
    return (
        <span
            className={`inline-block text-sm px-3 py-1 rounded-md border-2 border-b-5 border-gray-200 bg-white text-black shadow-md hover:translate-y-[1px] active:translate-y-[2px] hover:border-b-3 transition-transform duration-150 ${className}`}
        >
            {children}
        </span>
    );
};

export default Badge;
