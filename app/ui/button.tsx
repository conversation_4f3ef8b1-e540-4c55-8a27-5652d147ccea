// components/Button.tsx
import { cn } from "../lib/utils"; // optional: for merging Tailwind classes easily

type ButtonProps = {
    children: React.ReactNode;
    className?: string;
    onClick?: () => void;
};

const Button = ({ children, className, onClick }: ButtonProps) => {
    return (
        <button
            onClick={onClick}
            className={cn(
                "bg-white dark:bg-gray-800 text-sm cursor-pointer text-black dark:text-white border-2 border-b-6 border-gray-600 dark:border-gray-400 px-4 py-2 rounded-md active:border-b-[2px] focus:outline-none shadow-md hover:translate-y-[1px] active:translate-y-[2px] hover:border-b-3 transition-transform duration-150",
                className
            )}
        >
            {children}
        </button>
    );
};

export default Button;
