// components/Button.tsx
import { cn } from "../lib/utils"; // optional: for merging Tailwind classes easily

type ButtonProps = {
    children: React.ReactNode;
    className?: string;
    onClick?: () => void;
};

const Button = ({ children, className, onClick }: ButtonProps) => {
    return (
        <button
            onClick={onClick}
            className={cn(
                "bg-earthy-light dark:bg-earthy-dark text-sm cursor-pointer text-earthy-dark dark:text-earthy-light border-2 border-b-6 border-earthy dark:border-earthy-light px-4 py-2 rounded-md active:border-b-[2px] focus:outline-none shadow-md hover:translate-y-[1px] active:translate-y-[2px] hover:border-b-3 transition-all duration-150 hover:shadow-lg",
                className
            )}
        >
            {children}
        </button>
    );
};

export default Button;
