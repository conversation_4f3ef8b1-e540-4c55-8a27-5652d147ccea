{"name": "show-me", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "json-server": "json-server data/db.json --port 8000", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "@types/aos": "^3.0.7", "aos": "^2.3.4", "framer-motion": "^12.23.6", "isbot": "^5.1.27", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.5.3"}, "devDependencies": {"@react-router/dev": "^7.5.3", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "json-server": "1.0.0-beta.3", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-plugin-devtools-json": "^0.3.0", "vite-tsconfig-paths": "^5.1.4"}}